<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        /*
        当容器不足以横向摆放内容时，会在下一行进行摆放
        浮动只有左右浮动，没有上下浮动
        */
        .container{
            width: 500px;
            height: 200px;  /*如果不设置高度，进行浮动，会造成父元素高度塌陷*/
            background-color: #888;
        }

        .item{
            width: 200px;
            height: 200px;
            margin: 10px;
            background-color: rgb(80, 145, 231);
            float: left; /*设置会水平排放，并且新开一个页面*/
        }

        ul li{
            float: left;
            margin: 0 15px; /*上下 左右*/
        }
        
    </style>
</head>
<body>
    <div class="container">
        <div class="item"></div>
        <div class="item"></div>
        <div class="item"></div>
    </div>

    <ul>
        <li><a href="#">导航1</a></li>
        <li><a href="#">导航2</a></li>
        <li><a href="#">导航3</a></li>
    </ul>
</body>
</html>