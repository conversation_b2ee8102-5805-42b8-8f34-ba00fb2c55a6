<!DOCTYPE html>
<html>
    <head>
        <title>我的网页</title>
        <meta charset="utf-8">
    </head>
    <body>
        Hello,未来的开发工程师
        <h1 align="center">南通大学</h1>
        <h2 align="center">未来的开发工程师</h2>
        <h3 align="center">亓福祥</h3>
        
        <!-- <p>标签演示 - 段落元素 -->
        <p>这是第一个段落。段落标签用于组织文本内容，浏览器会自动在段落前后添加空白行。</p>
        <p>这是第二个段落。每个段落都是独立的文本块，有助于提高内容的可读性和结构性。</p>
        
        <!-- <br>标签演示 - 换行元素 -->
        <p>这里演示换行标签的使用：<br>
        第一行文本<br>
        第二行文本<br>
        第三行文本</p>
        
        <p>地址示例：<br>
        南通大学<br>
        江苏省南通市<br>
        226019</p>
        
        <!-- <hr>标签演示 - 水平分割线 -->
        <hr>
        
        <p>上面是一条水平分割线，用于分隔不同的内容区域。</p>
        
        <hr>
        
        <p>分割线常用于表示主题的转换或内容的分段。</p>
        
        <!-- <img>标签演示 - 图片元素 -->
        <img src="A.png" alt="图片A">
        
        <!-- <a>标签演示 - 超文本链接 -->
        <p>超文本链接演示：<a href="https://www.deepseek.com/">访问DeepSeek官网</a></p>
        
        <!-- 文本标签演示 - 各种文本格式 -->
        <p>文本标签演示：</p>
        <p><em>em标签 - 强调文本（斜体）</em></p>
        <p><b>b标签 - 粗体文本</b></p>
        <p><i>i标签 - 斜体文本</i></p>
        <p><strong>strong标签 - 重要文本（粗体）</strong></p>
        <p><del>del标签 - 删除线文本</del></p>
        <p><span>span标签 - 内联容器</span></p>
        
        <!-- <ol>标签演示 - 有序列表 -->
        <p>有序列表演示：</p>
        
        <!-- 默认数字列表 -->
        <ol>
            <li>默认数字列表项1</li>
            <li>默认数字列表项2</li>
            <li>默认数字列表项3</li>
        </ol>
        
        <!-- type="A" 大写字母列表 -->
        <ol type="A">
            <li>大写字母列表项A</li>
            <li>大写字母列表项B</li>
            <li>大写字母列表项C</li>
        </ol>
        
        <!-- type="a" 小写字母列表 -->
        <ol type="a">
            <li>小写字母列表项a</li>
            <li>小写字母列表项b</li>
            <li>小写字母列表项c</li>
        </ol>
        
        <!-- type="I" 大写罗马数字列表 -->
        <ol type="I">
            <li>大写罗马数字列表项I</li>
            <li>大写罗马数字列表项II</li>
            <li>大写罗马数字列表项III</li>
        </ol>
        
        <!-- type="i" 小写罗马数字列表 -->
        <ol type="i">
            <li>小写罗马数字列表项i</li>
            <li>小写罗马数字列表项ii</li>
            <li>小写罗马数字列表项iii</li>
        </ol>
        
        <!-- 嵌套有序列表演示 -->
        <ol>
            <li>主列表项1
                <ol type="a">
                    <li>子列表项a</li>
                    <li>子列表项b</li>
                </ol>
            </li>
            <li>主列表项2</li>
            <li>主列表项3</li>
        </ol>
        
        <!-- <ul>标签演示 - 无序列表 -->
        <p>无序列表演示：</p>
        <ul>
            <li>无序列表项1</li>
            <li>无序列表项2
                <ul>
                    <li>嵌套无序列表项1</li>
                    <li>嵌套无序列表项2
                        <ul>
                            <li>三级嵌套列表项1</li>
                            <li>三级嵌套列表项2</li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li>无序列表项3</li>
        </ul>
        
        <!-- <table>标签演示 - 表格 -->
        <p>表格演示：</p>
        <table border="1">
            <tr>
                <th>姓名</th>
                <th>年龄</th>
                <th>专业</th>
            </tr>
            <tr>
                <td>张三</td>
                <td>20</td>
                <td>计算机科学</td>
            </tr>
            <tr>
                <td>李四</td>
                <td>21</td>
                <td>软件工程</td>
            </tr>
            <tr>
                <td>王五</td>
                <td>19</td>
                <td>信息技术</td>
            </tr>
        </table>
        
        <h1></h1>
        <h2></h2>
        <h3></h3>
        <h4></h4>
        <h5></h5>
        <h6></h6>
        
        <!-- <em>标签演示 - 强调元素 -->
        <p>我喜欢码<em>python</em></p>
        
    </body>
</html>