<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .container{
            width: 500px; /*如果不设置宽度，由于是块级元素，会默认100%的设置*/
            background-color: #888;
        }

        .box{
            width: 100px;
            height: 100px;
            margin: 10px;
            background-color: rgb(80, 145, 231);
            float: left
        }

        .text{
            width: 100px;
            height: 100px;
            background-color: rgb(231, 145, 80);
            clear: both; /*无论是left和right浮动，都给清除*/
        }


    </style>    
</head>
<body>
    
    <div class="container">
        <div class="box"></div>
        <div class="box"></div>
        <div class="box"></div>
        <div class="text"></div>
    </div>

</body>
</html>