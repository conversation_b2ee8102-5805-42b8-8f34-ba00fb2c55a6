<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>

        .box{
            width: 500px;
            height: 500px;
            margin: 40px auto;
            background-color: #2c5aa0;
            border-radius: 10px;
            /*保留它是CSS最佳实践，有助于代码的可维护性和健壮性*/
            box-shadow:0 1px 2px rgba(0,0,0,0.3);
            animation: breathe 1s ease-in-out infinite alternate;
        }

        @keyframes breathe{
            0% {
                opacity: 0.2;
                box-shadow: 0 1px 2px rgba(255,255,255,0.1);
            }
            50% {
                opacity: 0.5;
                box-shadow: 0 1px 2px rgba(18,190,84,0.76);
            }
            100% {
                opacity: 1;
                box-shadow: 0 1px 2px rgba(255,255,255,0.1);
            }
        }

    </style>
</head>
<body>
    <div class="box"></div>
</body>
</html>