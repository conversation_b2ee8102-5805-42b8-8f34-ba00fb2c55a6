<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        /*span为一个内联容器*/
        .icon1{
            display: block; /*变为块级元素*/
            width: 55px;
            height: 70px;
            background-image: url(1.png);/*引入图片*/
            border: 1px solid red;
            background-position: -12px -14px;/*设置引入图片的显示位置*/
        }

        .icon2{
            display: block; /*变为块级元素*/
            width: 55px;
            height: 70px;
            background-image: url(1.png);
            border: 1px solid red;
            background-position: -170px -95px;
        }

    </style>
</head>
<body>
    <span class="icon1"></span>
    <span class="icon2"></span>
</body>
</html>