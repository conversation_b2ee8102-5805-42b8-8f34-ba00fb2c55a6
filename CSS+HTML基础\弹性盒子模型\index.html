<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>

        .container{
            width: 500px;
            height: 500px;
            background-color: #555;
            display:flex;
            flex-direction: column;
            /*
            row:横向从左到右排列(左对齐),默认的排列方式
            row-reverse:横向从右到左排列(右对齐)
            column:纵向从上到下排列(顶部对齐)
            column-reverse:纵向从下到上排列(底部对齐)
            */
            justify-content: flex-start;
            /*
            flex-start:顶部对齐
            flex-end:底部对齐
            center:居中对齐
            space-between:两端对齐，项目之间的间隔都相等。
            space-around:每个项目两侧的间隔相等。所以，项目之间的间隔比项目与边框的间隔大一倍。
            */
            align-items: stretch;

        }

        .box1{
            width: 100px;
            height: 100px;
            background-color: red;
            flex: 1;/*权重*/
        }
        
        .box2{
            width: 100px;
            height: 100px;
            background-color: green;
            flex: 1;
        }

        .box3{
            width: 100px;
            height: 100px;
            background-color: blue;
            flex: 1;
        }

    </style>
</head>
<body>

    <div class="container">
        <div class="box1"></div>
        <div class="box2"></div>
        <div class="box3"></div>
    </div>

</body>
</html>