<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>

        ul li{/*后代选择器*/
            color: red;
        }
        ul>li{/*子代选择器*/
            color:green
        }
        h3+p{/*h3下面相邻的兄弟元素*/
            color: blue;
        }
        h3~p{/*h3下面所有兄弟元素*/
            color: red;
        }
    </style>
</head>
<body>

    <ul>
        <li>列表1</li>
        <li>列表2</li>
        <li>列表3</li>
        <div>
            <ol>
                <li>列表4</li>
                <li>列表5</li>
            </ol>
        </div>
    </ul>

    <h3>我是标题</h3>
    <p>我是内容1</p>
    <p>我是内容2</p>

</body>
</html>